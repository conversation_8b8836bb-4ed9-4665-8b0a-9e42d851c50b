var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/hello/route.js")
R.c("server/chunks/eba44_next_79e306a8._.js")
R.c("server/chunks/[root-of-the-server]__60ce0fc0._.js")
R.m("[project]/OneDrive/Documents/Web Projects/next-helper/.next-internal/server/app/api/hello/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/OneDrive/Documents/Web Projects/next-helper/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/OneDrive/Documents/Web Projects/next-helper/app/api/hello/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/OneDrive/Documents/Web Projects/next-helper/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/OneDrive/Documents/Web Projects/next-helper/app/api/hello/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
