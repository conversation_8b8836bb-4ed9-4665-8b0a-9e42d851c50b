{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/next-helper/app/api/hello/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\r\n\r\n//Simple API Get Request\r\nexport async function GET(request: NextRequest) {\r\n  console.log(\"Hello World\");\r\n  return NextResponse.json({\r\n    message: \"Hello World\",\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe,IAAI,OAAoB;IAC5C,QAAQ,GAAG,CAAC;IACZ,OAAO,4MAAY,CAAC,IAAI,CAAC;QACvB,SAAS;IACX;AACF", "debugId": null}}]}