{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/next-helper/app/page.tsx"], "sourcesContent": ["export default async function Home() {\n  const res = await fetch(`http://localhost:3000/api/hello`);\n  const data = await res.json();\n\n  return (\n    <div className=\"flex flex-col h-screen mt-10 container mx-auto\">\n      <h1 className=\"text-2xl font-bold mb-4\">API Response:</h1>\n      <p className=\"text-lg\">{data.message}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAAe,eAAe;IAC5B,MAAM,MAAM,MAAM,MAAM,CAAC,+BAA+B,CAAC;IACzD,MAAM,OAAO,MAAM,IAAI,IAAI;IAE3B,qBACE,0SAAC;QAAI,WAAU;;0BACb,0SAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,0SAAC;gBAAE,WAAU;0BAAW,KAAK,OAAO;;;;;;;;;;;;AAG1C", "debugId": null}}]}