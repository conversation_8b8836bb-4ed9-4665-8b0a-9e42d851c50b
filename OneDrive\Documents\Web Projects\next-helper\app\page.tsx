import Link from "next/link";
import CardContainer from "@/components/ui/CardContainer";
import SimpleClientComponent from "./_components/SimpleClientComponent";
import ClientContainer from "./_components/ClientContainer";
import StillServerComponent from "./_components/StillServerComponent";

export default async function Home() {
  const res = await fetch(`http://localhost:3000/api/hello`);
  const data = await res.json();

  return (
    <div className="h-screen mt-20 container mx-auto gap-4">
      <h1 className="text-5xl font-bold mb-10">Home Page</h1>
      <div className="flex flex-row gap-4 flex-wrap w-full ">
        <CardContainer>
          <h1 className="text-2xl font-bold mb-4">Server API Response:</h1>
          <p className="text-lg">{data.message}</p>
        </CardContainer>
        <CardContainer>
          <SimpleClientComponent />
        </CardContainer>

        <ClientContainer>
          <StillServerComponent />
        </ClientContainer>
      </div>

      <div className="mt-10">
        <Link
          href="/other-page"
          className="text-white bg-zinc-700 px-4 py-2 rounded-lg hover:bg-zinc-600 font-bold text-2xl"
        >
          Other Page &gt;
        </Link>
      </div>
    </div>
  );
}
